version: '3.8'

services:
  # Flask Backend
  backend:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=***********************************************/connecting
      - MONGODB_HOST=mongo
      - OLLAMA_HOST=ollama
    depends_on:
      - postgres
      - mongo
      - redis
    volumes:
      - ./app:/app/app
      - ./migrations:/app/migrations
    networks:
      - connecting-network

  # React Frontend
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:5000
    depends_on:
      - backend
    networks:
      - connecting-network

  # PostgreSQL Database
  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=connecting
      - POSTGRES_USER=oracle
      - POSTGRES_PASSWORD=urimandthumim
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - connecting-network

  # MongoDB for Translation Cache
  mongo:
    image: mongo:6
    environment:
      - MONG<PERSON>_INITDB_ROOT_USERNAME=oracle
      - MONGO_INITDB_ROOT_PASSWORD=urimandthumim
      - MONGO_INITDB_DATABASE=connecting
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db
    networks:
      - connecting-network

  # Redis for Session Storage
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - connecting-network

  # Nginx Reverse Proxy
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - backend
      - frontend
    networks:
      - connecting-network

volumes:
  postgres_data:
  mongo_data:
  redis_data:

networks:
  connecting-network:
    driver: bridge
