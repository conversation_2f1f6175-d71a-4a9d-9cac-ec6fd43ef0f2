@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary-blue text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 ease-in-out;
    box-shadow: 0 4px 6px -1px rgba(9, 63, 180, 0.3);
  }

  .btn-primary:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 0 10px 15px -3px rgba(9, 63, 180, 0.4);
  }

  .btn-secondary {
    @apply bg-secondary-blue text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 ease-in-out;
    box-shadow: 0 4px 6px -1px rgba(0, 202, 255, 0.3);
  }

  .btn-secondary:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 0 10px 15px -3px rgba(0, 202, 255, 0.4);
  }

  .btn-success {
    @apply bg-primary-green text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 ease-in-out;
    box-shadow: 0 4px 6px -1px rgba(6, 146, 62, 0.3);
  }

  .btn-success:hover {
    @apply transform -translate-y-0.5;
    box-shadow: 0 10px 15px -3px rgba(6, 146, 62, 0.4);
  }
  
  .btn-outline {
    @apply border-2 border-primary-blue text-primary-blue px-6 py-3 rounded-xl font-medium transition-all duration-300 ease-in-out bg-transparent;
  }

  .btn-outline:hover {
    @apply bg-primary-blue text-white transform -translate-y-0.5;
    box-shadow: 0 10px 15px -3px rgba(9, 63, 180, 0.3);
  }

  .btn-outline-success {
    @apply border-2 border-primary-green text-primary-green px-6 py-3 rounded-xl font-medium transition-all duration-300 ease-in-out bg-transparent;
  }

  .btn-outline-success:hover {
    @apply bg-primary-green text-white transform -translate-y-0.5;
    box-shadow: 0 10px 15px -3px rgba(6, 146, 62, 0.3);
  }
  
  .input-field {
    @apply w-full px-4 py-3 border border-gray-200 rounded-xl bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-blue focus:border-transparent focus:bg-white transition-all duration-300 ease-in-out;
  }

  .input-field:focus {
    box-shadow: 0 0 0 3px rgba(9, 63, 180, 0.1);
  }
  
  .card {
    @apply bg-white rounded-xl shadow-lg p-6 border border-gray-100 backdrop-blur-sm;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .card-elevated {
    @apply bg-white rounded-xl p-6 border border-gray-100;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

  .post-card {
    @apply bg-white rounded-xl p-5 border border-gray-100 transition-all duration-300 ease-in-out;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .post-card:hover {
    @apply transform -translate-y-1;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }
  
  .sidebar {
    @apply bg-light-gray border-r border-gray-200 h-screen sticky top-0;
  }
  
  .main-content {
    @apply flex-1 max-w-2xl mx-auto;
  }
  
  .right-sidebar {
    @apply bg-light-gray border-l border-gray-200 h-screen sticky top-0;
  }
}

/* Gradient backgrounds */
.gradient-primary {
  background: linear-gradient(135deg, #093FB4 0%, #00CAFF 100%);
}

.gradient-success {
  background: linear-gradient(135deg, #06923E 0%, #10b981 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);
}

.gradient-orange {
  background: linear-gradient(135deg, #F59E0B 0%, #F97316 100%);
}

.gradient-subtle {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
}

.gradient-dark {
  background: linear-gradient(135deg, #1f2937 0%, #111827 100%);
}

/* Status colors */
.status-success {
  @apply bg-success/10 text-success border border-success/20;
}

.status-warning {
  @apply bg-warning/10 text-warning border border-warning/20;
}

.status-error {
  @apply bg-error/10 text-error border border-error/20;
}

.status-info {
  @apply bg-info/10 text-info border border-info/20;
}

/* Interactive elements */
.interactive-scale {
  @apply transition-transform duration-300 ease-in-out hover:scale-105 active:scale-95;
}

.interactive-glow {
  @apply transition-all duration-300 ease-in-out;
}

.interactive-glow:hover {
  box-shadow: 0 0 20px rgba(9, 63, 180, 0.3);
}

.interactive-bounce {
  @apply transition-transform duration-300 ease-in-out hover:animate-bounce;
}

.interactive-pulse {
  @apply transition-all duration-300 ease-in-out hover:animate-pulse;
}

.interactive-slide {
  @apply transition-transform duration-300 ease-in-out hover:translate-x-1;
}

/* Loading states */
.skeleton {
  @apply bg-gray-200 dark:bg-gray-700 animate-pulse rounded;
}

.shimmer {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Modern glass effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Typography utilities */
.text-heading-1 {
  @apply text-4xl font-bold leading-tight tracking-tight;
}

.text-heading-2 {
  @apply text-3xl font-bold leading-tight tracking-tight;
}

.text-heading-3 {
  @apply text-2xl font-semibold leading-snug;
}

.text-heading-4 {
  @apply text-xl font-semibold leading-snug;
}

.text-body-large {
  @apply text-lg leading-relaxed;
}

.text-body {
  @apply text-base leading-relaxed;
}

.text-body-small {
  @apply text-sm leading-relaxed;
}

.text-caption {
  @apply text-xs leading-normal text-gray-600 dark:text-gray-400;
}

/* Spacing utilities */
.section-padding {
  @apply py-12 px-6;
}

.container-padding {
  @apply px-4 sm:px-6 lg:px-8;
}

.content-spacing {
  @apply space-y-6;
}

.content-spacing-large {
  @apply space-y-8;
}

/* Layout utilities */
.page-container {
  @apply max-w-7xl mx-auto container-padding;
}

.content-container {
  @apply max-w-4xl mx-auto;
}

.sidebar-container {
  @apply max-w-xs;
}

/* Dark mode styles */
.dark {
  @apply bg-gray-900 text-white;
}

.dark .card {
  @apply bg-gray-800 border-gray-700;
}

.dark .post-card {
  @apply bg-gray-800 border-gray-700 hover:bg-gray-700;
}

.dark .sidebar {
  @apply bg-gray-800 border-gray-700;
}

.dark .right-sidebar {
  @apply bg-gray-800 border-gray-700;
}

.dark .input-field {
  @apply bg-gray-700 border-gray-600 text-white placeholder-gray-400;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-in-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* Conversation tree styles */
.conversation-tree {
  border-left: 2px solid #e5e7eb;
  margin-left: 1rem;
  padding-left: 1rem;
}

.conversation-branch {
  border-left: 2px solid #093FB4;
  margin-left: 1rem;
  padding-left: 1rem;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #093FB4;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Notification badge */
.notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .sidebar {
    @apply hidden;
  }
  
  .right-sidebar {
    @apply hidden;
  }
  
  .main-content {
    @apply max-w-full px-4;
  }
}
